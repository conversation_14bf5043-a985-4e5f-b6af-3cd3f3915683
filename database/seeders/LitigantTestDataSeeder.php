<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Litigant;

class LitigantTestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $litigants = [
            [
                'full_name' => '<PERSON>uyễn <PERSON>',
                'id_number' => '123456789012',
                'issued_place' => 'Công an TP. Hà Nội',
                'issued_date' => '2020-01-15',
                'address' => '<PERSON><PERSON> 123, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TP<PERSON>'
            ],
            [
                'full_name' => 'Trần <PERSON>',
                'id_number' => '987654321098',
                'issued_place' => 'Công an TP. H<PERSON> Ch<PERSON>',
                'issued_date' => '2019-05-20',
                'address' => '<PERSON><PERSON> 456, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Quận 1, T<PERSON><PERSON> <PERSON><PERSON>'
            ],
            [
                'full_name' => '<PERSON><PERSON> <PERSON>',
                'id_number' => '456789123456',
                'issued_place' => 'Công an tỉnh Đà Nẵng',
                'issued_date' => '2021-03-10',
                'address' => 'Số 789, Đường Trần Phú, Phường Thạch Thang, Quận Hải Châu, TP. Đà Nẵng'
            ],
            [
                'full_name' => 'Phạm Thị Dung',
                'id_number' => '789123456789',
                'issued_place' => 'Công an tỉnh Hải Phòng',
                'issued_date' => '2018-12-05',
                'address' => 'Số 321, Đường Lê Lợi, Phường Máy Chai, Quận Ngô Quyền, TP. Hải Phòng'
            ],
            [
                'full_name' => 'Hoàng Văn Em',
                'id_number' => '321654987321',
                'issued_place' => 'Công an tỉnh Cần Thơ',
                'issued_date' => '2022-07-18',
                'address' => 'Số 654, Đường 3/2, Phường Xuân Khánh, Quận Ninh Kiều, TP. Cần Thơ'
            ]
        ];

        foreach ($litigants as $litigant) {
            Litigant::create($litigant);
        }

        $this->command->info('Test litigant data created successfully!');
    }
}
