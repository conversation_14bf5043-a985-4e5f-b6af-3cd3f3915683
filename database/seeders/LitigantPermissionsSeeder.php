<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class LitigantPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create litigant permissions
        $permissions = [
            ['name' => 'litigants.view', 'guard_name' => 'web'],
            ['name' => 'litigants.create', 'guard_name' => 'web'],
            ['name' => 'litigants.edit', 'guard_name' => 'web'],
            ['name' => 'litigants.delete', 'guard_name' => 'web'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate($permission);
        }

        // Assign permissions to existing roles
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        $adminRole = Role::where('name', 'Admin')->first();
        $notaryStaffRole = Role::where('name', 'Notary Staff')->first();
        $viewerRole = Role::where('name', 'Viewer')->first();

        if ($superAdminRole) {
            // Super Admin gets all litigant permissions
            $litigantPermissions = Permission::where('name', 'like', 'litigants.%')->get();
            foreach ($litigantPermissions as $permission) {
                $superAdminRole->givePermissionTo($permission);
            }
        }

        if ($adminRole) {
            // Admin gets all litigant permissions
            $litigantPermissions = Permission::where('name', 'like', 'litigants.%')->get();
            foreach ($litigantPermissions as $permission) {
                $adminRole->givePermissionTo($permission);
            }
        }

        if ($notaryStaffRole) {
            // Notary Staff gets view, create, edit permissions
            $staffPermissions = Permission::whereIn('name', [
                'litigants.view', 'litigants.create', 'litigants.edit'
            ])->get();
            foreach ($staffPermissions as $permission) {
                $notaryStaffRole->givePermissionTo($permission);
            }
        }

        if ($viewerRole) {
            // Viewer gets only view permission
            $viewPermission = Permission::where('name', 'litigants.view')->first();
            if ($viewPermission) {
                $viewerRole->givePermissionTo($viewPermission);
            }
        }

        $this->command->info('Litigant permissions created and assigned successfully!');
    }
}
