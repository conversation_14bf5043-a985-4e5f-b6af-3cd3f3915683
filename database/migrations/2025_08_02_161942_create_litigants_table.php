<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('litigants', function (Blueprint $table) {
            $table->id();
            $table->string('full_name')->comment('Họ và tên đương sự');
            $table->string('id_number', 20)->unique()->comment('Số CCCD/CMND');
            $table->string('issued_place')->comment('Nơi cấp CCCD/CMND');
            $table->date('issued_date')->comment('Ngày cấp CCCD/CMND');
            $table->text('address')->comment('Địa chỉ thường trú');
            $table->timestamps();
            $table->softDeletes();

            // Indexes for better performance
            $table->index('full_name');
            $table->index('issued_place');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('litigants');
    }
};
