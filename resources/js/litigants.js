/**
 * Litigants Management JavaScript
 * Handles CRUD operations for litigants with AJAX
 */

// Global variables
let litigantsTable;
let deleteLitigantId = null;
let isEditMode = false;

// Initialize when DOM is ready
$(document).ready(function() {
  // Check if routes are available
  if (!checkRoutes(window.litigantsRoutes, 'Litigants')) {
    return;
  }

  initializeDataTable();
  initializeEventHandlers();
  setMaxDate();
});

/**
 * Initialize DataTable for litigants
 */
function initializeDataTable() {
  litigantsTable = $('#litigantsTable').DataTable({
    processing: true,
    ajax: {
      url: window.litigantsRoutes.index,
      data: function(d) {
        d.search_name = $('#searchName').val();
        d.search_id_number = $('#searchIdNumber').val();
        d.search_issued_place = $('#searchIssuedPlace').val();
      },
      dataSrc: 'data',
      error: function(xhr, error, thrown) {
        console.error('DataTable AJAX error:', error);
        showToast('error', 'C<PERSON> lỗi xảy ra khi tải dữ liệu');
      }
    },
    columns: [
      {
        data: 'full_name',
        render: function(data, type, row) {
          return `<strong>${data}</strong>`;
        }
      },
      {
        data: 'id_number',
        orderable: false
      },
      {
        data: 'issued_place'
      },
      {
        data: 'issued_date',
        className: 'text-center'
      },
      {
        data: 'address',
        render: function(data, type, row) {
          return data.length > 50 ? data.substring(0, 50) + '...' : data;
        }
      },
      {
        data: 'created_at',
        className: 'text-center'
      },
      {
        data: 'action',
        orderable: false,
        searchable: false,
        className: 'text-center'
      }
    ],
    order: [[5, 'desc']], // Sort by created_at desc
    language: {
      url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
    },
    responsive: true,
    pageLength: 25,
    lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
    dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
    drawCallback: function(settings) {
      // Add fade-in animation to new rows
      $('#litigantsTable tbody tr').addClass('fade-in');
    }
  });
}

/**
 * Initialize event handlers
 */
function initializeEventHandlers() {
  // Form submission
  $('#litigantForm').on('submit', function(e) {
    e.preventDefault();
    saveLitigant();
  });

  // Modal events
  $('#litigantModal').on('hidden.bs.modal', function() {
    resetForm();
  });

  // Search on Enter key
  $('#searchName, #searchIdNumber, #searchIssuedPlace').on('keypress', function(e) {
    if (e.which === 13) {
      applyFilters();
    }
  });

  // ID number input formatting
  $('#litigantIdNumber').on('input', function() {
    // Remove non-numeric characters
    this.value = this.value.replace(/[^0-9]/g, '');
  });
}

/**
 * Set maximum date for issued_date to today
 */
function setMaxDate() {
  const today = new Date().toISOString().split('T')[0];
  $('#litigantIssuedDate').attr('max', today);
}

/**
 * Apply filters and reload table
 */
function applyFilters() {
  litigantsTable.ajax.reload();
}

/**
 * Clear all filters
 */
function clearFilters() {
  $('#searchName').val('');
  $('#searchIdNumber').val('');
  $('#searchIssuedPlace').val('');
  litigantsTable.ajax.reload();
}

/**
 * Save litigant (create or update)
 */
function saveLitigant() {
  const form = $('#litigantForm');
  const formData = new FormData(form[0]);
  const litigantId = $('#litigantId').val();
  
  let url = window.litigantsRoutes.store;
  let method = 'POST';
  
  if (isEditMode && litigantId) {
    url = `${window.litigantsRoutes.update}/${litigantId}`;
    method = 'PUT';
    formData.append('_method', 'PUT');
  }

  // Clear previous validation errors
  clearValidationErrors();

  // Show loading state
  const submitButton = form.find('button[type="submit"]');
  const originalText = submitButton.html();
  submitButton.html('<span class="spinner-border spinner-border-sm me-2"></span>Đang xử lý...').prop('disabled', true);

  $.ajax({
    url: url,
    method: 'POST',
    data: formData,
    processData: false,
    contentType: false,
    success: function(response) {
      if (response.success) {
        showToast('success', response.message);
        $('#litigantModal').modal('hide');
        litigantsTable.ajax.reload();
      } else {
        showToast('error', response.message || 'Có lỗi xảy ra');
      }
    },
    error: function(xhr) {
      if (xhr.status === 422) {
        // Validation errors
        const errors = xhr.responseJSON.errors;
        displayValidationErrors(errors);
        showToast('error', 'Vui lòng kiểm tra lại thông tin đã nhập');
      } else {
        const message = xhr.responseJSON?.message || 'Có lỗi xảy ra khi lưu dữ liệu';
        showToast('error', message);
      }
    },
    complete: function() {
      // Restore button state
      submitButton.html(originalText).prop('disabled', false);
    }
  });
}

/**
 * Edit litigant
 */
function editLitigant(id) {
  isEditMode = true;
  
  $.ajax({
    url: `${window.litigantsRoutes.show}/${id}`,
    method: 'GET',
    success: function(litigant) {
      $('#litigantId').val(litigant.id);
      $('#litigantFullName').val(litigant.full_name);
      $('#litigantIdNumber').val(litigant.id_number);
      $('#litigantIssuedPlace').val(litigant.issued_place);
      $('#litigantIssuedDate').val(litigant.issued_date);
      $('#litigantAddress').val(litigant.address);
      
      $('#litigantModalTitle').text('Chỉnh sửa đương sự');
      $('#submitButtonText').text('Cập nhật đương sự');
      
      $('#litigantModal').modal('show');
    },
    error: function(xhr) {
      const message = xhr.responseJSON?.message || 'Có lỗi xảy ra khi tải dữ liệu';
      showToast('error', message);
    }
  });
}

/**
 * Delete litigant
 */
function deleteLitigant(id) {
  deleteLitigantId = id;
  $('#deleteModal').modal('show');
}

/**
 * Confirm delete
 */
function confirmDelete() {
  if (!deleteLitigantId) return;

  $.ajax({
    url: `${window.litigantsRoutes.destroy}/${deleteLitigantId}`,
    method: 'DELETE',
    data: {
      _token: $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      if (response.success) {
        showToast('success', response.message);
        $('#deleteModal').modal('hide');
        litigantsTable.ajax.reload();
      } else {
        showToast('error', response.message || 'Có lỗi xảy ra');
      }
    },
    error: function(xhr) {
      const message = xhr.responseJSON?.message || 'Có lỗi xảy ra khi xóa dữ liệu';
      showToast('error', message);
    },
    complete: function() {
      deleteLitigantId = null;
    }
  });
}

/**
 * Reset form
 */
function resetForm() {
  $('#litigantForm')[0].reset();
  $('#litigantId').val('');
  $('#litigantModalTitle').text('Thêm đương sự');
  $('#submitButtonText').text('Thêm đương sự');
  isEditMode = false;
  clearValidationErrors();
}

/**
 * Display validation errors
 */
function displayValidationErrors(errors) {
  Object.keys(errors).forEach(function(field) {
    const input = $(`[name="${field}"]`);
    input.addClass('is-invalid');
    
    // Remove existing error message
    input.siblings('.invalid-feedback').remove();
    
    // Add error message
    input.after(`<div class="invalid-feedback">${errors[field][0]}</div>`);
  });
}

/**
 * Clear validation errors
 */
function clearValidationErrors() {
  $('.is-invalid').removeClass('is-invalid');
  $('.invalid-feedback').remove();
}

/**
 * Check if required routes are available
 */
function checkRoutes(routes, moduleName) {
  if (!routes) {
    console.error(`${moduleName} routes not found`);
    showToast('error', `Lỗi cấu hình: Không tìm thấy routes cho ${moduleName}`);
    return false;
  }
  return true;
}

/**
 * Show toast notification
 */
function showToast(type, message) {
  // Implementation depends on your toast library
  // This is a placeholder - you should implement based on your project's toast system
  if (typeof toastr !== 'undefined') {
    toastr[type](message);
  } else {
    alert(message);
  }
}
