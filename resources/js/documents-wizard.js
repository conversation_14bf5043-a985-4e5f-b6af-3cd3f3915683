/**
 * Documents Wizard
 */

'use strict';

$(document).ready(function() {
  // Global variables
  let validationStepper;
  let currentTemplateFields = [];
  let partiesCount = 0;
  let assetsCount = 0;

  // Initialize wizard
  const $wizardCreateDocument = $('#wizard-create-document');
  
  // Check if wizard element exists
  if (!$wizardCreateDocument.length) {
    console.error('Wizard element not found');
    return;
  }

  console.log('Initializing wizard...');

  // Wizard form
  const $wizardCreateDocumentForm = $('#wizard-create-document-form');

  // Wizard steps - check if they exist
  const $wizardStep1 = $('#contract-type-step');
  const $wizardStep2 = $('#template-step');
  const $wizardStep3 = $('#parties-step');
  const $wizardStep4 = $('#assets-step');
  const $wizardStep5 = $('#review-step');

  // Validate step elements exist
  if (!$wizardStep1.length || !$wizardStep2.length || !$wizardStep3.length || !$wizardStep4.length || !$wizardStep5.length) {
    console.error('One or more wizard steps not found');
    return;
  }

  // Wizard navigation buttons
  const $wizardNext = $('.btn-next');
  const $wizardPrev = $('.btn-prev');

  // Check if Stepper is available
  if (typeof Stepper === 'undefined') {
    console.error('Stepper library not loaded');
    return;
  }

  // Initialize stepper with error handling
  try {
    validationStepper = new Stepper($wizardCreateDocument[0], {
      linear: true
    });
    console.log('Stepper initialized successfully');
  } catch (error) {
    console.error('Error initializing stepper:', error);
    return;
  }

  // Check if FormValidation is available
  if (typeof FormValidation === 'undefined') {
    console.error('FormValidation library not loaded');
    return;
  }

  // Wait for DOM to be fully ready and validate required elements
  setTimeout(function() {
    initializeFormValidations();
  }, 100);

  function initializeFormValidations() {
    // Double check that step elements exist
    if (!$wizardStep1[0]) {
      console.error('Step 1 element not found for FormValidation');
      return;
    }

    // Check if contract_type_id field exists
    const contractTypeField = document.getElementById('contract_type_id');
    if (!contractTypeField) {
      console.error('contract_type_id field not found');
      return;
    }

    console.log('Initializing FormValidation1 for element:', $wizardStep1[0]);
    console.log('Contract type field found:', contractTypeField);

    // Step 1: Contract Type validation
    let FormValidation1;
    try {
      FormValidation1 = FormValidation.formValidation($wizardStep1[0], {
        fields: {
          contract_type_id: {
            validators: {
              notEmpty: {
                message: 'Vui lòng chọn loại hợp đồng'
              }
            }
          }
        },
        plugins: {
          trigger: new FormValidation.plugins.Trigger(),
          bootstrap5: new FormValidation.plugins.Bootstrap5({
            eleValidClass: '',
            rowSelector: '.d-none, .row'
          }),
          autoFocus: new FormValidation.plugins.AutoFocus(),
          submitButton: new FormValidation.plugins.SubmitButton()
        }
      }).on('core.form.valid', function () {
        console.log('Step 1 validation passed');
        loadTemplatesByContractType();
        if (validationStepper && typeof validationStepper.next === 'function') {
          validationStepper.next();
        }
      });

      console.log('FormValidation1 initialized successfully');

      // Store FormValidation1 globally for navigation handlers
      window.FormValidation1 = FormValidation1;

    } catch (error) {
      console.error('Error initializing FormValidation1:', error);
      console.error('Error details:', error.stack);
      return;
    }

    // Initialize Step 2 validation with a small delay to ensure DOM is ready
    setTimeout(function() {
      initializeStep2Validation();
    }, 200);
  }

  function initializeStep2Validation() {
    console.log('Attempting to initialize FormValidation2...');

    // Double check that step 2 element exists
    if (!$wizardStep2 || !$wizardStep2.length || !$wizardStep2[0]) {
      console.error('Step 2 element not found for FormValidation2');
      return;
    }

    // Check if the hidden input field exists
    const assetTemplateField = document.getElementById('asset_template_id');
    if (!assetTemplateField) {
      console.error('asset_template_id field not found for FormValidation2');
      return;
    }

    console.log('Step 2 element found:', $wizardStep2[0]);
    console.log('Asset template field found:', assetTemplateField);

    // Step 2: Template validation
    let FormValidation2;
    try {
      FormValidation2 = FormValidation.formValidation($wizardStep2[0], {
        fields: {
          asset_template_id: {
            validators: {
              notEmpty: {
                message: 'Vui lòng chọn template'
              }
            }
          }
        },
        plugins: {
          trigger: new FormValidation.plugins.Trigger(),
          bootstrap5: new FormValidation.plugins.Bootstrap5({
            eleValidClass: '',
            rowSelector: '.d-none, .row'
          }),
          autoFocus: new FormValidation.plugins.AutoFocus(),
          submitButton: new FormValidation.plugins.SubmitButton()
        }
      }).on('core.form.valid', function () {
        console.log('Step 2 validation passed');
        loadTemplateFields();
        if (validationStepper && typeof validationStepper.next === 'function') {
          validationStepper.next();
        }
      });

      console.log('FormValidation2 initialized successfully');

      // Store FormValidation2 globally for navigation handlers
      window.FormValidation2 = FormValidation2;

    } catch (error) {
      console.error('Error initializing FormValidation2:', error);
      console.error('Error details:', error.stack);

      // Try to initialize again after a longer delay
      setTimeout(function() {
        console.log('Retrying FormValidation2 initialization...');
        try {
          if ($wizardStep2 && $wizardStep2.length && $wizardStep2[0] && document.getElementById('asset_template_id')) {
            const retryFormValidation2 = FormValidation.formValidation($wizardStep2[0], {
              fields: {
                asset_template_id: {
                  validators: {
                    notEmpty: {
                      message: 'Vui lòng chọn template'
                    }
                  }
                }
              },
              plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap5: new FormValidation.plugins.Bootstrap5({
                  eleValidClass: '',
                  rowSelector: '.d-none, .row'
                })
              }
            });

            window.FormValidation2 = retryFormValidation2;
            console.log('FormValidation2 retry successful');
          }
        } catch (retryError) {
          console.error('FormValidation2 retry also failed:', retryError);
        }
      }, 500);

      return;
    }
  }

  // Initialize contract type card selection
  $(document).on('click', '.contract-type-card', function() {
    const $card = $(this);
    const contractTypeId = $card.data('contract-type-id');

    console.log('Contract type card clicked:', contractTypeId);

    // Remove selected class from all cards
    $('.contract-type-card').removeClass('selected');

    // Add selected class to clicked card
    $card.addClass('selected');

    // Update hidden input for FormValidation
    $('#contract_type_id').val(contractTypeId);

    // Revalidate if FormValidation1 exists
    if (window.FormValidation1 && typeof window.FormValidation1.revalidateField === 'function') {
      window.FormValidation1.revalidateField('contract_type_id');
    }

    console.log('Contract type selected:', contractTypeId);
  });

  // Navigation event handlers
  $wizardNext.on('click', function(event) {
    event.preventDefault();
    event.stopPropagation();
    
    if (!validationStepper) {
      console.error('Stepper not initialized');
      return;
    }
    
    const currentIndex = validationStepper._currentIndex || 0;
    console.log('Next button clicked, current step:', currentIndex);
    
    try {
      switch (currentIndex) {
        case 0:
          if (window.FormValidation1 && typeof window.FormValidation1.validate === 'function') {
            console.log('Validating step 1...');
            window.FormValidation1.validate();
          } else {
            console.error('FormValidation1 not available');
            // Fallback: check if contract type is selected
            const contractTypeId = $('#contract_type_id').val();
            if (contractTypeId) {
              console.log('Contract type selected, proceeding to next step');
              loadTemplatesByContractType();
              if (validationStepper && typeof validationStepper.next === 'function') {
                validationStepper.next();
              }
            } else {
              alert('Vui lòng chọn loại hợp đồng');
            }
          }
          break;
        case 1:
          if (window.FormValidation2 && typeof window.FormValidation2.validate === 'function') {
            console.log('Validating step 2...');
            window.FormValidation2.validate();
          } else {
            console.error('FormValidation2 not available');
            // Fallback: check if template is selected using hidden input
            const templateId = $('#asset_template_id').val();
            if (templateId) {
              console.log('Template selected via fallback, proceeding to next step');
              loadTemplateFields();
              if (validationStepper && typeof validationStepper.next === 'function') {
                validationStepper.next();
              }
            } else {
              alert('Vui lòng chọn template');
            }
          }
          break;
        default:
          console.log('Unknown step:', currentIndex);
          break;
      }
    } catch (error) {
      console.error('Error in next button handler:', error);
    }
  });

  $wizardPrev.on('click', function(event) {
    event.preventDefault();
    event.stopPropagation();
    
    if (!validationStepper) {
      console.error('Stepper not initialized');
      return;
    }
    
    const currentIndex = validationStepper._currentIndex || 0;
    console.log('Previous button clicked, current step:', currentIndex);
    
    try {
      switch (currentIndex) {
        case 1:
          if (typeof validationStepper.previous === 'function') {
            validationStepper.previous();
          } else {
            console.error('Stepper previous method not available');
          }
          break;
        case 0:
        default:
          console.log('Already at first step');
          break;
      }
    } catch (error) {
      console.error('Error in previous button handler:', error);
    }
  });

  // Load templates by contract type
  function loadTemplatesByContractType() {
    const contractTypeId = $('#contract_type_id').val();
    console.log('Loading templates for contract type:', contractTypeId);

    if (!contractTypeId) {
      console.warn('No contract type selected');
      return;
    }

    const $container = $('#templates-container');
    $container.html('<div class="loading-container"><div class="loading-spinner"></div> Đang tải templates...</div>');

    $.ajax({
      url: `/documents/ajax/templates-by-contract-type?contract_type_id=${contractTypeId}`,
      type: 'GET',
      dataType: 'json',
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(templates) {
        console.log('Templates loaded:', templates);
        $container.empty();

        if (!templates || templates.length === 0) {
          $container.html('<div class="empty-container">Không có template nào cho loại hợp đồng này</div>');
          return;
        }

        templates.forEach(template => {
          const templateCard = createTemplateCard(template);
          $container.append(templateCard);
        });

        // Ensure FormValidation2 is initialized after templates are loaded
        if (!window.FormValidation2) {
          console.log('FormValidation2 not found, attempting to initialize...');
          setTimeout(function() {
            initializeStep2Validation();
          }, 100);
        }
      },
      error: function(xhr, status, error) {
        console.error('Error loading templates:', {xhr, status, error});
        console.error('Response text:', xhr.responseText);
        $container.html('<div class="error-container">Có lỗi xảy ra khi tải templates: ' + error + '</div>');
      }
    });
  }

  // Create template card
  function createTemplateCard(template) {
    console.log('Creating template card for:', template);
    const $col = $('<div class="col-md-6 col-lg-4 mb-3"></div>');

    const cardHtml = `
      <div class="template-card" data-template-id="${template.id}">
        ${template.is_default ? '<span class="badge bg-primary template-badge">Mặc định</span>' : ''}
        <div class="template-title">${template.name || 'Không có tên'}</div>
        <div class="template-description">${template.description || 'Không có mô tả'}</div>
        <div class="template-actions">
          <button type="button" class="btn btn-sm btn-outline-info" onclick="previewTemplate(${template.id})">
            <i class="ri-eye-line me-1"></i>Preview
          </button>
          <button type="button" class="btn btn-sm btn-primary select-template-btn" data-template-id="${template.id}">
            <i class="ri-check-line me-1"></i>Chọn
          </button>
        </div>
      </div>
    `;

    $col.html(cardHtml);

    // Add click handler for template selection button
    $col.find('.select-template-btn').on('click', function(event) {
      event.preventDefault();
      event.stopPropagation();
      console.log('Template select button clicked:', template.id);

      try {
        selectTemplate(template.id);
      } catch (error) {
        console.error('Error in select button click handler:', error);
      }
    });

    // Add click handler for entire card (but not when clicking buttons)
    $col.find('.template-card').on('click', function(event) {
      // Don't trigger if clicking on buttons
      if ($(event.target).closest('.template-actions').length) {
        return;
      }

      event.preventDefault();
      console.log('Template card clicked:', template.id);

      try {
        selectTemplate(template.id);
      } catch (error) {
        console.error('Error in card click handler:', error);
      }
    });

    return $col[0];
  }

  // Select template
  function selectTemplate(templateId) {
    console.log('Selecting template:', templateId);

    // Validate templateId
    if (!templateId) {
      console.error('Template ID is required');
      return;
    }

    try {
      // Remove selected class from all template cards
      $('.template-card').removeClass('selected');

      // Find the specific template card
      const $targetCard = $(`.template-card[data-template-id="${templateId}"]`);
      if (!$targetCard.length) {
        console.error('Template card not found for ID:', templateId);
        return;
      }

      // Add selected class to clicked template card
      $targetCard.addClass('selected');

      // Update hidden input field for FormValidation
      const $hiddenInput = $('#asset_template_id');
      if ($hiddenInput.length) {
        $hiddenInput.val(templateId);
        console.log('Hidden input updated for template:', templateId);
      } else {
        console.warn('Hidden input field not found for asset_template_id');
      }

      // Also update any radio buttons if they exist (for backward compatibility)
      $('input[name="asset_template_id"][type="radio"]').prop('checked', false);
      const $radioButton = $(`input[name="asset_template_id"][type="radio"][value="${templateId}"]`);
      if ($radioButton.length) {
        $radioButton.prop('checked', true);
        console.log('Radio button also updated for template:', templateId);
      }

      // Revalidate if FormValidation2 exists and is properly initialized
      if (window.FormValidation2 && typeof window.FormValidation2.revalidateField === 'function') {
        try {
          // Check if the hidden input field exists before revalidating
          const fieldElement = document.getElementById('asset_template_id');
          if (fieldElement) {
            window.FormValidation2.revalidateField('asset_template_id');
            console.log('FormValidation2 revalidated for asset_template_id');
          } else {
            console.warn('asset_template_id hidden field not found for validation');
          }
        } catch (validationError) {
          console.error('Error during FormValidation2 revalidation:', validationError);
        }
      } else {
        console.warn('FormValidation2 not available for revalidation');
      }

      console.log('Template selection completed successfully:', templateId);

    } catch (error) {
      console.error('Error in selectTemplate function:', error);
    }
  }

  // Global function for template preview
  window.previewTemplate = function(templateId) {
    console.log('Previewing template:', templateId);
    // Implementation for template preview
  };

  // Global function to manually initialize FormValidation2 if needed
  window.initializeFormValidation2 = function() {
    console.log('Manual FormValidation2 initialization requested');
    if (!window.FormValidation2) {
      initializeStep2Validation();
    } else {
      console.log('FormValidation2 already initialized');
    }
  };

  function loadTemplateFields() {
    console.log('Loading template fields...');
    // Implementation for loading template fields
  }

});
