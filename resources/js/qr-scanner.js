/**
 * QR Scanner functionality
 */

'use strict';

$(document).ready(function() {
  let html5QrcodeScanner = null;
  let currentScanType = null; // 'party' or 'asset'

  // Initialize QR Scanner when modal is shown
  $('#qrScannerModal').on('shown.bs.modal', function () {
    initializeQRScanner();
  });

  // Cleanup when modal is hidden
  $('#qrScannerModal').on('hidden.bs.modal', function () {
    cleanupQRScanner();
  });

  function initializeQRScanner() {
    const $qrReaderElement = $('#qr-reader');
    if ($qrReaderElement.length === 0) return;

    // Clear previous content
    $qrReaderElement.empty();
    $('#qr-reader-results').empty();

    // Check if camera is available
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      showQRError('Camera không được hỗ trợ trên thiết bị này');
      return;
    }

    // Try to use Html5Qrcode library if available
    if (typeof Html5Qrcode !== 'undefined') {
      initializeHtml5QrScanner();
    } else {
      // Fallback to basic camera access
      initializeBasicCamera();
    }
  }

  function initializeHtml5QrScanner() {
    try {
      html5QrcodeScanner = new Html5QrcodeScanner(
        "qr-reader",
        {
          fps: 10,
          qrbox: { width: 250, height: 250 },
          aspectRatio: 1.0
        },
        false
      );

      html5QrcodeScanner.render(onScanSuccess, onScanFailure);
    } catch (error) {
      console.error('Error initializing Html5Qrcode:', error);
      initializeBasicCamera();
    }
  }

  function initializeBasicCamera() {
    const qrReaderElement = document.getElementById('qr-reader');

    // Create video element
    const video = document.createElement('video');
    video.style.width = '100%';
    video.style.maxWidth = '400px';
    video.style.height = 'auto';
    video.autoplay = true;
    video.playsInline = true;

    qrReaderElement.appendChild(video);

    // Request camera access
    navigator.mediaDevices.getUserMedia({
      video: {
        facingMode: 'environment' // Use back camera if available
      }
    })
    .then(function(stream) {
      video.srcObject = stream;

      // Add manual input option
      addManualInputOption();

      // Show instructions
      showQRInstructions();
    })
    .catch(function(error) {
      console.error('Error accessing camera:', error);
      showQRError('Không thể truy cập camera. Vui lòng kiểm tra quyền truy cập.');
      addManualInputOption();
    });
  }

  function onScanSuccess(decodedText, decodedResult) {
    console.log('QR Code scanned:', decodedText);

    try {
      const qrData = JSON.parse(decodedText);
      processQRData(qrData);
    } catch (error) {
      // If not JSON, treat as plain text
      processPlainTextQR(decodedText);
    }

    // Close modal after successful scan
    $('#qrScannerModal').modal('hide');
  }

  function onScanFailure(error) {
    // Handle scan failure silently
    // console.warn('QR scan failed:', error);
  }

  function processQRData(qrData) {
    const resultsElement = document.getElementById('qr-reader-results');

    if (currentScanType === 'party') {
      // Use the new party processing function from documents-wizard.js
      if (typeof window.processPartyQRData === 'function') {
        window.processPartyQRData(qrData);
      } else {
        processPartyQRData(qrData);
      }
    } else if (currentScanType === 'asset') {
      processAssetQRData(qrData);
    }

    resultsElement.innerHTML = `
      <div class="qr-result">
        <strong>QR Code đã được quét thành công!</strong><br>
        Dữ liệu đã được điền vào form.
      </div>
    `;
  }

  function processPartyQRData(qrData) {
    // Expected QR data format for party:
    // {
    //   "type": "party",
    //   "full_name": "Nguyễn Văn A",
    //   "birth_year": 1990,
    //   "id_number": "123456789",
    //   "id_type": "cccd",
    //   "current_address": "123 ABC Street",
    //   "phone": "0123456789",
    //   "email": "<EMAIL>"
    // }

    if (qrData.type !== 'party') {
      showQRError('QR Code không phải dành cho thông tin đương sự');
      return;
    }

    // Find the last party form or create new one
    let partyForms = document.querySelectorAll('.party-item');
    let targetForm = partyForms[partyForms.length - 1];

    if (!targetForm || isPartyFormFilled(targetForm)) {
      // Add new party form if current one is filled
      if (typeof addPartyForm === 'function') {
        addPartyForm();
        partyForms = document.querySelectorAll('.party-item');
        targetForm = partyForms[partyForms.length - 1];
      }
    }

    if (targetForm) {
      fillPartyForm(targetForm, qrData);
    }
  }

  function processAssetQRData(qrData) {
    // Expected QR data format for asset:
    // {
    //   "type": "asset",
    //   "asset_name": "Nhà số 123",
    //   "asset_code": "ASSET001",
    //   "field_values": {
    //     "dia_chi": "123 ABC Street",
    //     "dien_tich": "100",
    //     "gia_tri": "1000000000"
    //   }
    // }

    if (qrData.type !== 'asset') {
      showQRError('QR Code không phải dành cho thông tin tài sản');
      return;
    }

    // Find the last asset form or create new one
    let assetForms = document.querySelectorAll('.asset-item');
    let targetForm = assetForms[assetForms.length - 1];

    if (!targetForm || isAssetFormFilled(targetForm)) {
      // Add new asset form if current one is filled
      if (typeof addAssetForm === 'function') {
        addAssetForm();
        assetForms = document.querySelectorAll('.asset-item');
        targetForm = assetForms[assetForms.length - 1];
      }
    }

    if (targetForm) {
      fillAssetForm(targetForm, qrData);
    }
  }

  function processPlainTextQR(text) {
    const resultsElement = document.getElementById('qr-reader-results');

    // For parties, try to process as ID number
    if (currentScanType === 'party') {
      if (typeof window.processPartyQRData === 'function') {
        window.processPartyQRData(text);
        return;
      }
    }

    resultsElement.innerHTML = `
      <div class="qr-result">
        <strong>QR Code Text:</strong><br>
        ${text}<br>
        <small>Vui lòng nhập thông tin thủ công.</small>
      </div>
    `;
  }

  function fillPartyForm(form, data) {
    const fields = {
      'full_name': data.full_name,
      'birth_year': data.birth_year,
      'id_number': data.id_number,
      'id_type': data.id_type || 'cccd',
      'current_address': data.current_address,
      'phone': data.phone,
      'email': data.email,
      'gender': data.gender,
      'occupation': data.occupation
    };

    Object.keys(fields).forEach(fieldName => {
      if (fields[fieldName]) {
        const input = form.querySelector(`[name*="[${fieldName}]"]`);
        if (input) {
          input.value = fields[fieldName];

          // Trigger change event for validation
          const event = new Event('change', { bubbles: true });
          input.dispatchEvent(event);
        }
      }
    });
  }

  function fillAssetForm(form, data) {
    // Fill basic asset info
    const assetNameInput = form.querySelector('[name*="[asset_name]"]');
    const assetCodeInput = form.querySelector('[name*="[asset_code]"]');

    if (assetNameInput && data.asset_name) {
      assetNameInput.value = data.asset_name;
    }

    if (assetCodeInput && data.asset_code) {
      assetCodeInput.value = data.asset_code;
    }

    // Fill dynamic field values
    if (data.field_values) {
      Object.keys(data.field_values).forEach(fieldName => {
        const input = form.querySelector(`[name*="[field_values][${fieldName}]"]`);
        if (input) {
          input.value = data.field_values[fieldName];

          // Trigger change event for validation
          const event = new Event('change', { bubbles: true });
          input.dispatchEvent(event);
        }
      });
    }
  }

  function isPartyFormFilled(form) {
    const requiredFields = ['full_name', 'birth_year', 'id_number', 'current_address'];
    return requiredFields.some(fieldName => {
      const input = form.querySelector(`[name*="[${fieldName}]"]`);
      return input && input.value.trim() !== '';
    });
  }

  function isAssetFormFilled(form) {
    const assetNameInput = form.querySelector('[name*="[asset_name]"]');
    return assetNameInput && assetNameInput.value.trim() !== '';
  }

  function addManualInputOption() {
    const qrReaderElement = document.getElementById('qr-reader');

    const manualInputDiv = document.createElement('div');
    manualInputDiv.className = 'mt-3';
    manualInputDiv.innerHTML = `
      <div class="text-center">
        <p class="text-muted">Hoặc nhập mã QR thủ công:</p>
        <div class="input-group">
          <input type="text" id="manualQRInput" class="form-control" placeholder="Nhập mã QR...">
          <button type="button" class="btn btn-primary" onclick="processManualQR()">
            <i class="ri-check-line"></i>
          </button>
        </div>
      </div>
    `;

    qrReaderElement.appendChild(manualInputDiv);
  }

  function showQRInstructions() {
    const qrReaderElement = document.getElementById('qr-reader');

    const instructionsDiv = document.createElement('div');
    instructionsDiv.className = 'mt-2 text-center';
    instructionsDiv.innerHTML = `
      <small class="text-muted">
        <i class="ri-information-line"></i>
        Hướng camera về phía mã QR để quét tự động
      </small>
    `;

    qrReaderElement.appendChild(instructionsDiv);
  }

  function showQRError(message) {
    const resultsElement = document.getElementById('qr-reader-results');
    resultsElement.innerHTML = `
      <div class="alert alert-danger">
        <i class="ri-error-warning-line me-2"></i>
        ${message}
      </div>
    `;
  }

  function cleanupQRScanner() {
    if (html5QrcodeScanner) {
      try {
        html5QrcodeScanner.clear();
      } catch (error) {
        console.warn('Error clearing QR scanner:', error);
      }
      html5QrcodeScanner = null;
    }

    // Stop any video streams
    const video = document.querySelector('#qr-reader video');
    if (video && video.srcObject) {
      const tracks = video.srcObject.getTracks();
      tracks.forEach(track => track.stop());
    }

    currentScanType = null;
  }

  // Global function for manual QR processing
  window.processManualQR = function() {
    const input = document.getElementById('manualQRInput');
    if (input && input.value.trim()) {
      onScanSuccess(input.value.trim());
    }
  };

  // Global function to set scan type
  window.openQRScanner = function(type) {
    currentScanType = type;
    $('#qrScannerModal').modal('show');
  };

});
