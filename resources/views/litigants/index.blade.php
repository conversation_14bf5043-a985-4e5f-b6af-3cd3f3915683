@php
$configData = Helper::appClasses();
@endphp
@extends('layouts/layoutMaster')

@section('title', 'Quản lý đương sự')

<!-- Vendor Style -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss'
])
@endsection

<!-- Page Style -->
@section('page-style')
@vite(['resources/css/litigants.css'])
@endsection

<!-- Vendor Script -->
@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'
])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header border-bottom">
        <h5 class="card-title mb-0">
          <i class="ri-user-line me-2"></i>
          Quản lý đương sự
        </h5>
        <div class="d-flex justify-content-between align-items-center row pt-4 gap-4 gap-md-0">
          <div class="col-md-4 col-12">
            @can('litigants.create')
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#litigantModal">
              <i class="ri-add-line me-1"></i>
              Thêm đương sự
            </button>
            @endcan
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="card-body border-bottom">
        <div class="row g-3">
          <div class="col-md-4">
            <div class="form-floating form-floating-outline">
              <input type="text" id="searchName" class="form-control" placeholder="Tìm theo tên">
              <label for="searchName">Tìm theo tên</label>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-floating form-floating-outline">
              <input type="text" id="searchIdNumber" class="form-control" placeholder="Tìm theo CCCD/CMND">
              <label for="searchIdNumber">Tìm theo CCCD/CMND</label>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-floating form-floating-outline">
              <input type="text" id="searchIssuedPlace" class="form-control" placeholder="Tìm theo nơi cấp">
              <label for="searchIssuedPlace">Tìm theo nơi cấp</label>
            </div>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-12">
            <button type="button" class="btn btn-outline-primary me-2" onclick="applyFilters()">
              <i class="ri-search-line me-1"></i>
              Tìm kiếm
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
              <i class="ri-refresh-line me-1"></i>
              Xóa bộ lọc
            </button>
          </div>
        </div>
      </div>

      <!-- DataTable -->
      <div class="card-datatable table-responsive">
        <table id="litigantsTable" class="datatables-litigants table">
          <thead>
            <tr>
              <th>Họ và tên</th>
              <th>CCCD/CMND</th>
              <th>Nơi cấp</th>
              <th>Ngày cấp</th>
              <th>Địa chỉ</th>
              <th>Ngày tạo</th>
              <th>Thao tác</th>
            </tr>
          </thead>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Litigant Modal -->
<div class="modal fade" id="litigantModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="litigantModalTitle">Thêm đương sự</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="litigantForm">
        <div class="modal-body">
          <input type="hidden" id="litigantId" name="id">

          <div class="row g-3">
            <div class="col-12">
              <div class="form-floating form-floating-outline">
                <input type="text" id="litigantFullName" name="full_name" class="form-control" required>
                <label for="litigantFullName">Họ và tên *</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="litigantIdNumber" name="id_number" class="form-control" required maxlength="20">
                <label for="litigantIdNumber">Số CCCD/CMND *</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="litigantIssuedPlace" name="issued_place" class="form-control" required>
                <label for="litigantIssuedPlace">Nơi cấp *</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="date" id="litigantIssuedDate" name="issued_date" class="form-control" required>
                <label for="litigantIssuedDate">Ngày cấp *</label>
              </div>
            </div>
            <div class="col-12">
              <div class="form-floating form-floating-outline">
                <textarea id="litigantAddress" name="address" class="form-control" rows="3" required></textarea>
                <label for="litigantAddress">Địa chỉ thường trú *</label>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="submit" class="btn btn-primary">
            <span id="submitButtonText">Thêm đương sự</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Xác nhận xóa</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Bạn có chắc chắn muốn xóa đương sự này không?</p>
        <p class="text-muted">Hành động này không thể hoàn tác.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
        <button type="button" class="btn btn-danger" onclick="confirmDelete()">Xóa</button>
      </div>
    </div>
  </div>
</div>
@endsection

<!-- Page Script -->
@section('page-script')
@vite('resources/js/litigants.js')
<script>
// Pass routes to JavaScript
window.litigantsRoutes = {
  index: '{{ route("litigants.index") }}',
  store: '{{ route("litigants.store") }}',
  show: '{{ url("litigants") }}',
  update: '{{ url("litigants") }}',
  destroy: '{{ url("litigants") }}'
};
</script>
@endsection
