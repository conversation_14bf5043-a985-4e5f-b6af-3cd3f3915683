/* Documents Wizard Styles */

/* Horizontal Layout */
.wizard-horizontal {
  display: flex;
  flex-direction: column;
}

.wizard-horizontal .bs-stepper-header {
  display: flex !important;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  flex-direction: row !important;
  border-bottom: 1px solid var(--bs-gray-200);
  border-right: none !important;
  padding-bottom: 1rem;
  margin-bottom: 2rem;
  width: 100% !important;
  min-width: auto !important;
}

.wizard-horizontal .bs-stepper-content {
  width: 100%;
  padding-top: 1rem;
}

.wizard-horizontal .step {
  display: flex;
  align-items: center;
  margin: 0;
}

.wizard-horizontal .step-trigger {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  padding: 0.5rem;
  text-decoration: none;
  color: inherit;
}

.wizard-horizontal .bs-stepper-label {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 0.5rem;
}

.wizard-horizontal .bs-stepper-title {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--bs-gray-800);
}

.wizard-horizontal .bs-stepper-subtitle {
  font-size: 0.75rem;
  color: var(--bs-gray-600);
}

.wizard-horizontal .bs-stepper-number {
  display: none; /* Hide numbers in horizontal layout as we use circles */
}

.wizard-horizontal .line {
  flex: 1;
  height: 2px;
  background-color: var(--bs-gray-200);
  margin: 0 1rem;
  min-height: 2px;
}

.wizard-horizontal .step.active ~ .line,
.wizard-horizontal .step.completed ~ .line {
  background-color: var(--bs-primary);
}

/* Vertical Layout (for mobile) */
.wizard-vertical .bs-stepper-header {
  min-width: 15rem;
}

.wizard-vertical .bs-stepper-content {
  width: 100%;
}

/* Override bs-stepper default styles for horizontal layout */
.wizard-horizontal.bs-stepper {
  display: flex !important;
  flex-direction: column !important;
}

.wizard-horizontal.bs-stepper .bs-stepper-header {
  display: flex !important;
  flex-direction: row !important;
  border-right: none !important;
  border-bottom: 1px solid var(--bs-gray-200) !important;
  width: 100% !important;
  min-width: auto !important;
}

/* Step styling */
.bs-stepper-circle {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--bs-gray-100);
  border: 2px solid var(--bs-gray-300);
  color: var(--bs-gray-600);
  font-weight: 600;
}

.step.active .bs-stepper-circle {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
  color: white;
}

.step.completed .bs-stepper-circle {
  background-color: var(--bs-success);
  border-color: var(--bs-success);
  color: white;
}

/* Contract Type Cards */
.contract-type-card {
  position: relative;
}

.contract-type-card .card {
  border: 2px solid var(--bs-gray-200);
  transition: all 0.3s ease;
  position: relative;
}

.contract-type-card .card:hover {
  border-color: var(--bs-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.contract-type-card.selected .card {
  border-color: var(--bs-primary);
  background-color: rgba(var(--bs-primary-rgb), 0.05);
  box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.2);
}

.contract-type-card .contract-type-icon {
  margin-bottom: 1rem;
}

.contract-type-card .radio-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid var(--bs-gray-300);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  transition: all 0.3s ease;
}

.contract-type-card.selected .radio-indicator {
  border-color: var(--bs-primary);
  background-color: var(--bs-primary);
  color: white;
}

.contract-type-card .radio-indicator i {
  font-size: 0.875rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.contract-type-card.selected .radio-indicator i {
  opacity: 1;
}

/* Content sections */
.content-header {
  border-bottom: 1px solid var(--bs-gray-200);
  padding-bottom: 1rem;
  margin-bottom: 2rem;
}

.content-header h4 {
  color: var(--bs-gray-800);
  font-weight: 600;
}

.content-header small {
  color: var(--bs-gray-600);
}

/* Template selection */
.template-card {
  border: 2px solid var(--bs-gray-200);
  border-radius: 0.5rem;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background-color: white;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.template-card:hover {
  border-color: var(--bs-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.template-card.selected {
  border-color: var(--bs-primary);
  background-color: rgba(var(--bs-primary-rgb), 0.05);
  box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.2);
}

.template-card .template-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  z-index: 1;
}

.template-card .template-title {
  font-weight: 600;
  color: var(--bs-gray-800);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.template-card .template-description {
  color: var(--bs-gray-600);
  font-size: 0.875rem;
  margin-bottom: 1rem;
  flex-grow: 1;
  line-height: 1.4;
}

.template-card .template-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: auto;
}

.template-card .template-actions .btn {
  flex: 1;
}

/* Party and Asset forms */
.party-item,
.asset-item {
  border: 1px solid var(--bs-gray-200);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1rem;
  position: relative;
  background-color: var(--bs-gray-50);
}

.party-item .remove-btn,
.asset-item .remove-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

.party-item h6,
.asset-item h6 {
  color: var(--bs-gray-800);
  font-weight: 600;
  margin-bottom: 1rem;
  padding-right: 2rem;
}

/* Dynamic fields */
.field-group {
  border: 1px solid var(--bs-gray-200);
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.field-group-title {
  font-weight: 600;
  color: var(--bs-gray-800);
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--bs-gray-200);
}

/* QR Scanner */
.qr-scanner-container {
  text-align: center;
  padding: 2rem;
}

#qr-reader {
  border: 2px dashed var(--bs-gray-300);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

#qr-reader video {
  width: 100%;
  max-width: 400px;
  border-radius: 0.375rem;
}

.qr-result {
  background-color: var(--bs-success-bg-subtle);
  border: 1px solid var(--bs-success-border-subtle);
  color: var(--bs-success-text-emphasis);
  padding: 0.75rem;
  border-radius: 0.375rem;
  margin-top: 1rem;
}

/* Search results */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--bs-gray-200);
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.search-result-item {
  padding: 0.75rem;
  border-bottom: 1px solid var(--bs-gray-100);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.search-result-item:hover {
  background-color: var(--bs-gray-50);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-name {
  font-weight: 600;
  color: var(--bs-gray-800);
}

.search-result-details {
  font-size: 0.875rem;
  color: var(--bs-gray-600);
}

/* Party search results */
.party-search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--bs-gray-200);
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 250px;
  overflow-y: auto;
}

.party-search-item {
  padding: 0.75rem;
  border-bottom: 1px solid var(--bs-gray-100);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.party-search-item:hover {
  background-color: var(--bs-gray-50);
}

.party-search-item:last-child {
  border-bottom: none;
}

/* Party items in container */
.party-item {
  position: relative;
}

.party-item .card {
  border: 1px solid var(--bs-gray-200);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.party-item .card:hover {
  border-color: var(--bs-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Review section */
.review-section {
  border: 1px solid var(--bs-gray-200);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.review-section-title {
  font-weight: 600;
  color: var(--bs-gray-800);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--bs-gray-200);
}

.review-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--bs-gray-100);
}

.review-item:last-child {
  border-bottom: none;
}

.review-label {
  font-weight: 500;
  color: var(--bs-gray-700);
}

.review-value {
  color: var(--bs-gray-800);
  text-align: right;
}

/* Loading states */
.loading-spinner {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border: 3px solid var(--bs-gray-300);
  border-radius: 50%;
  border-top-color: var(--bs-primary);
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 0.5rem;
}

.loading-container {
  text-align: center;
  padding: 2rem;
  color: var(--bs-gray-600);
}

.error-container {
  text-align: center;
  padding: 2rem;
  color: var(--bs-danger);
  background-color: var(--bs-danger-bg-subtle);
  border: 1px solid var(--bs-danger-border-subtle);
  border-radius: 0.5rem;
  margin: 1rem 0;
}

.empty-container {
  text-align: center;
  padding: 2rem;
  color: var(--bs-gray-500);
  background-color: var(--bs-gray-50);
  border: 1px solid var(--bs-gray-200);
  border-radius: 0.5rem;
  margin: 1rem 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  /* Switch to vertical layout on mobile */
  .wizard-horizontal {
    display: flex;
    flex-direction: column;
  }

  .wizard-horizontal .bs-stepper-header {
    flex-direction: column;
    border-bottom: none;
    border-right: 1px solid var(--bs-gray-200);
    padding-bottom: 0;
    padding-right: 1rem;
    margin-bottom: 0;
    margin-right: 1rem;
    min-width: 15rem;
  }

  .wizard-horizontal .bs-stepper-content {
    padding-top: 0;
    padding-left: 1rem;
  }

  .wizard-horizontal .line {
    width: 2px;
    height: 1rem;
    margin: 0.5rem 0;
  }

  .wizard-vertical .bs-stepper-header {
    min-width: auto;
    width: 100%;
  }

  .wizard-vertical {
    flex-direction: column;
  }

  .template-card {
    padding: 1rem;
  }

  .party-item,
  .asset-item {
    padding: 1rem;
  }

  /* Stack steps vertically on mobile */
  .wizard-horizontal .bs-stepper-header {
    flex-direction: column;
    align-items: stretch;
  }

  .wizard-horizontal .step {
    margin-bottom: 1rem;
  }

  .wizard-horizontal .line {
    display: none;
  }
}

/* Form validation styles */
.is-invalid {
  border-color: var(--bs-danger);
}

.invalid-feedback {
  display: block;
  color: var(--bs-danger);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Success states */
.is-valid {
  border-color: var(--bs-success);
}

.valid-feedback {
  display: block;
  color: var(--bs-success);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}
