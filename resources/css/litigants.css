/**
 * Litigants Management Styles
 * Custom styles for litigants CRUD functionality
 */

/* Card styling */
.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.card-header .card-title {
  color: white;
  font-weight: 600;
}

.card-header .card-title i {
  color: rgba(255, 255, 255, 0.8);
}

/* Filter section styling */
.card-body.border-bottom {
  background-color: #f8f9fa;
}

/* Form styling */
.form-floating-outline {
  position: relative;
}

.form-floating-outline .form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-floating-outline label {
  color: #6c757d;
}

/* Modal styling */
.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom: none;
}

.modal-header .modal-title {
  color: white;
  font-weight: 600;
}

.modal-header .btn-close {
  filter: invert(1);
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  border-top: 1px solid #e9ecef;
  padding: 1rem 2rem;
}

/* Button styling */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.btn-outline-primary {
  border-color: #667eea;
  color: #667eea;
}

.btn-outline-primary:hover {
  background-color: #667eea;
  border-color: #667eea;
}

/* DataTable styling */
.datatables-litigants {
  border-collapse: separate;
  border-spacing: 0;
}

.datatables-litigants thead th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #667eea;
  font-weight: 600;
  color: #495057;
  padding: 1rem 0.75rem;
}

.datatables-litigants tbody tr {
  transition: all 0.3s ease;
}

.datatables-litigants tbody tr:hover {
  background-color: rgba(102, 126, 234, 0.05);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.datatables-litigants tbody td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
  border-bottom: 1px solid #e9ecef;
}

/* Action buttons */
.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}

.btn-outline-warning:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

.btn-outline-danger:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* Code styling for ID numbers */
code.text-primary {
  background-color: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 600;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-dialog {
    margin: 1rem;
  }
  
  .modal-body {
    padding: 1rem;
  }
  
  .card-header .d-flex {
    flex-direction: column;
    gap: 1rem;
  }
  
  .datatables-litigants {
    font-size: 0.875rem;
  }
  
  .datatables-litigants thead th,
  .datatables-litigants tbody td {
    padding: 0.5rem 0.25rem;
  }
}

/* Success/Error message styling */
.alert {
  border: none;
  border-radius: 0.5rem;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
}

.alert-success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
}

/* Form validation styling */
.is-invalid {
  border-color: #dc3545;
}

.invalid-feedback {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Search highlight */
.search-highlight {
  background-color: rgba(255, 235, 59, 0.3);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h5 {
  margin-bottom: 0.5rem;
  color: #495057;
}

.empty-state p {
  margin-bottom: 0;
  font-size: 0.875rem;
}
