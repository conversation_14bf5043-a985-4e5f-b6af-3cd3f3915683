<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class Litigant extends Model
{
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'full_name',
        'id_number',
        'issued_place',
        'issued_date',
        'address',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'issued_date' => 'date',
    ];

    /**
     * Scope to search by name
     */
    public function scopeSearchByName(Builder $query, string $name): Builder
    {
        return $query->where('full_name', 'like', '%' . $name . '%');
    }

    /**
     * Scope to search by ID number
     */
    public function scopeSearchByIdNumber(Builder $query, string $idNumber): Builder
    {
        return $query->where('id_number', 'like', '%' . $idNumber . '%');
    }

    /**
     * Scope to search by issued place
     */
    public function scopeSearchByIssuedPlace(Builder $query, string $place): Builder
    {
        return $query->where('issued_place', 'like', '%' . $place . '%');
    }

    /**
     * Get formatted issued date
     */
    public function getFormattedIssuedDateAttribute(): string
    {
        return $this->issued_date ? $this->issued_date->format('d/m/Y') : '';
    }

    /**
     * Get short address (first 50 characters)
     */
    public function getShortAddressAttribute(): string
    {
        return strlen($this->address) > 50
            ? substr($this->address, 0, 50) . '...'
            : $this->address;
    }
}
