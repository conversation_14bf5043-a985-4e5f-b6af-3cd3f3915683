<?php

namespace App\Http\Controllers;

use App\Models\Litigant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class LitigantController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Check permission
        if (!Auth::user()->hasPermission('litigants.view')) {
            abort(403, 'Bạn không có quyền xem danh sách đương sự');
        }

        if ($request->ajax()) {
            $query = Litigant::query();

            // Apply filters
            if ($request->filled('search_name')) {
                $query->searchByName($request->search_name);
            }

            if ($request->filled('search_id_number')) {
                $query->searchByIdNumber($request->search_id_number);
            }

            if ($request->filled('search_issued_place')) {
                $query->searchByIssuedPlace($request->search_issued_place);
            }

            $litigants = $query->orderBy('created_at', 'desc')->get();

            $data = $litigants->map(function ($litigant) {
                $actions = '';

                if (Auth::user()->hasPermission('litigants.edit')) {
                    $actions .= '<button type="button" class="btn btn-sm btn-outline-warning me-1" onclick="editLitigant(' . $litigant->id . ')">
                        <i class="ri-edit-line"></i>
                    </button>';
                }

                if (Auth::user()->hasPermission('litigants.delete')) {
                    $actions .= '<button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteLitigant(' . $litigant->id . ')">
                        <i class="ri-delete-bin-line"></i>
                    </button>';
                }

                return [
                    'id' => $litigant->id,
                    'full_name' => $litigant->full_name,
                    'id_number' => '<code class="text-primary">' . $litigant->id_number . '</code>',
                    'issued_place' => $litigant->issued_place,
                    'issued_date' => $litigant->formatted_issued_date,
                    'address' => $litigant->short_address,
                    'created_at' => $litigant->created_at->format('d/m/Y H:i'),
                    'action' => $actions
                ];
            });

            return response()->json(['data' => $data]);
        }

        return view('litigants.index');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check permission
        if (!Auth::user()->hasPermission('litigants.create')) {
            abort(403, 'Bạn không có quyền tạo đương sự');
        }

        $request->validate([
            'full_name' => 'required|string|max:255',
            'id_number' => 'required|string|max:20|unique:litigants,id_number',
            'issued_place' => 'required|string|max:255',
            'issued_date' => 'required|date|before_or_equal:today',
            'address' => 'required|string',
        ], [
            'full_name.required' => 'Họ và tên là bắt buộc',
            'id_number.required' => 'Số CCCD/CMND là bắt buộc',
            'id_number.unique' => 'Số CCCD/CMND đã tồn tại trong hệ thống',
            'issued_place.required' => 'Nơi cấp là bắt buộc',
            'issued_date.required' => 'Ngày cấp là bắt buộc',
            'issued_date.before_or_equal' => 'Ngày cấp không được lớn hơn ngày hiện tại',
            'address.required' => 'Địa chỉ là bắt buộc',
        ]);

        try {
            $litigant = Litigant::create($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Đương sự đã được tạo thành công',
                'data' => $litigant,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tạo đương sự: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Litigant $litigant)
    {
        // Check permission
        if (!Auth::user()->hasPermission('litigants.view')) {
            abort(403, 'Bạn không có quyền xem đương sự');
        }

        return response()->json($litigant);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Litigant $litigant)
    {
        // Check permission
        if (!Auth::user()->hasPermission('litigants.edit')) {
            abort(403, 'Bạn không có quyền chỉnh sửa đương sự');
        }

        $request->validate([
            'full_name' => 'required|string|max:255',
            'id_number' => 'required|string|max:20|unique:litigants,id_number,' . $litigant->id,
            'issued_place' => 'required|string|max:255',
            'issued_date' => 'required|date|before_or_equal:today',
            'address' => 'required|string',
        ], [
            'full_name.required' => 'Họ và tên là bắt buộc',
            'id_number.required' => 'Số CCCD/CMND là bắt buộc',
            'id_number.unique' => 'Số CCCD/CMND đã tồn tại trong hệ thống',
            'issued_place.required' => 'Nơi cấp là bắt buộc',
            'issued_date.required' => 'Ngày cấp là bắt buộc',
            'issued_date.before_or_equal' => 'Ngày cấp không được lớn hơn ngày hiện tại',
            'address.required' => 'Địa chỉ là bắt buộc',
        ]);

        try {
            $litigant->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Đương sự đã được cập nhật thành công',
                'data' => $litigant,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật đương sự: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Litigant $litigant)
    {
        // Check permission
        if (!Auth::user()->hasPermission('litigants.delete')) {
            abort(403, 'Bạn không có quyền xóa đương sự');
        }

        try {
            $litigant->delete();

            return response()->json([
                'success' => true,
                'message' => 'Đương sự đã được xóa thành công',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa đương sự: ' . $e->getMessage(),
            ], 500);
        }
    }
}
